#include "paintingitem.h"
#include "paintingitempainter.h"

PaintingItem::PaintingItem(QQuickItem *parent)
    : QNanoQuickItem(parent)
{
    setHighQualityRendering(true);
}

void PaintingItem::setBrushWidth(qreal width)
{
    if (qFuzzyCompare(m_brushWidth, width))
        return;
    m_brushWidth = width;
    Q_EMIT brushWidthChanged();
    update();
}

void PaintingItem::setBrushColor(const QColor &color)
{
    if (m_brushColor == color)
        return;
    m_brushColor = color;
    Q_EMIT brushColorChanged();
    update();
}

void PaintingItem::setBackgroundColor(const QColor &color)
{
    if (m_backgroundColor == color)
        return;
    m_backgroundColor = color;
    Q_EMIT backgroundColorChanged();
    update();
}

QNanoQuickItemPainter* PaintingItem::createItemPainter() const
{
    PaintingItemPainter *itemPainter = new PaintingItemPainter();
    QObject::connect(itemPainter, &PaintingItemPainter::update, this, &PaintingItem::update);
    return itemPainter;
}

void PaintingItem::mousePressEvent(QMouseEvent *event)
{
    // Start a new line
    m_pointsSynced = 0;
    m_points.clear();
    m_points.append(event->pos());
    update();
}

void PaintingItem::mouseReleaseEvent(QMouseEvent *event)
{
    Q_UNUSED(event);
    // Use INT_MAX as a mark that drawing the line has ended
    m_points.append(QPoint(INT_MAX, 0));
    update();
}

void PaintingItem::mouseMoveEvent(QMouseEvent *event)
{
    m_points.append(event->pos());
    update();
}
