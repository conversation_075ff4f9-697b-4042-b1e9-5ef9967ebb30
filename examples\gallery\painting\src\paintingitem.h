#ifndef PAINTINGITEM_H
#define PAINTINGITEM_H

#include "qnanoquickitem.h"
#include <QVector>

class QNanoQuickItemPainter;

class PaintingItem: public QNanoQuickItem
{
    Q_OBJECT
    Q_PROPERTY(qreal brushWidth READ brushWidth WRITE setBrushWidth NOTIFY brushWidthChanged)
    Q_PROPERTY(QColor brushColor READ brushColor WRITE setBrushColor NOTIFY brushColorChanged)
    Q_PROPERTY(QColor backgroundColor READ backgroundColor WRITE setBackgroundColor NOTIFY backgroundColorChanged)

public:
    PaintingItem(QQuickItem *parent = nullptr);

    // Reimplement
    QNanoQuickItemPainter *createItemPainter() const Q_DECL_OVERRIDE Q_DECL_FINAL;

    qreal brushWidth() const { return m_brushWidth; }
    void setBrushWidth(qreal width);

    QColor brushColor() const { return m_brushColor; }
    void setBrushColor(const QColor &color);

    QColor backgroundColor() const { return m_backgroundColor; }
    void setBackgroundColor(const QColor &color);

Q_SIGNALS:
    void brushWidthChanged();
    void brushColorChanged();
    void backgroundColorChanged();

protected:
    // Reimplement from QQuickItem
    void mousePressEvent(QMouseEvent *event) Q_DECL_OVERRIDE Q_DECL_FINAL;
    void mouseReleaseEvent(QMouseEvent *event) Q_DECL_OVERRIDE Q_DECL_FINAL;
    void mouseMoveEvent(QMouseEvent *event) Q_DECL_OVERRIDE Q_DECL_FINAL;

private:
    friend class PaintingItemPainter;
    QVector<QPoint> m_points;
    int m_pointsSynced = 0;
    qreal m_brushWidth = 2.0;
    QColor m_brushColor = QColor("#FF0000");
    QColor m_backgroundColor = QColor("#234641");
};

#endif // PAINTINGITEM_H
