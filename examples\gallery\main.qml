import QtQuick 2.4
import QtQuick.Window 2.2

Window {
    id: mainWindow

    property real dp: Math.min(width, height) / 400
    property string title: "Freehand Painting"
    property real topbarHeight: Math.floor(50 * dp)

    width: 1300
    height: 1000
    visible: true
    color: "#234641"

    Component.onCompleted: {
        galleryLoader.loadDemo("painting");
    }

    ListModel {
        id: galleryModel
        // ListElement {
        //     name: "QNanoPainter Features"
        //     path: "qnanopainter_features"
        // }
        // ListElement {
        //     name: "Mouse Event Boxes"
        //     path: "mouse_events"
        // }
        ListElement {
            name: "Freehand Painting"
            path: "painting"
        }
    }

    ListView {
        id: listView
        anchors.top: parent.top
        anchors.bottom: parent.bottom
        width: parent.width
        orientation: ListView.Vertical
        maximumFlickVelocity: 10000
        highlightMoveDuration: 2500
        model: galleryModel
        visible: false
        opacity: 0
        Behavior on opacity {
            NumberAnimation {
                duration: 200
                easing.type: Easing.InOutQuad
            }
        }
        delegate: Item {
            width: listView.width
            height: 75 * dp
            Text {
                id: delegateTextItem
                anchors.centerIn: parent
                font.pixelSize: 20 * dp
                color: "#b0b0b0"
                text: model.name
                opacity: delegateMouseArea.pressed ? 0.4 : 1.0
            }
            MouseArea {
                id: delegateMouseArea
                anchors.fill: parent
                onClicked: {
                    mainWindow.title = model.name;
                    galleryLoader.loadDemo(model.path);
                }
            }
            Image {
                anchors.verticalCenter: parent.verticalCenter
                anchors.right: parent.right
                anchors.rightMargin: 25 * dp
                width: 20 * dp
                height: 20 * dp
                rotation: 180
                source: "back.png"
                opacity: delegateMouseArea.pressed ? 0.4 : 1.0
            }
            Rectangle {
                anchors.horizontalCenter: parent.horizontalCenter
                anchors.bottom: parent.bottom
                width: parent.width * 0.9
                height: Math.ceil(1 * dp)
                color: "#606060"
            }
        }
    }



    Loader {
        id: galleryLoader

        function loadDemo(path) {
            galleryLoader.source = path + "/main.qml";
        }

        function closeDemo() {
            mainWindow.title = "";
            galleryLoader.source = "";
        }

        anchors.fill: parent

        Behavior on source {
            SequentialAnimation {
                NumberAnimation {
                    target: galleryLoader
                    property: "opacity"
                    to: 0
                    duration: galleryLoader.source == "" ? 0 : 500
                    easing.type: Easing.InOutQuad
                }
                PropertyAction {
                    target: galleryLoader
                    property: "source"
                }
                NumberAnimation {
                    target: galleryLoader
                    property: "opacity"
                    to: 1
                    duration: 500
                    easing.type: Easing.InOutQuad
                }
            }
        }
    }




}
