/**********************************************************
** Copyright (c) 2018 QUIt Coding <<EMAIL>>
**
** This software is provided 'as-is', without any express or implied
** warranty.  In no event will the authors be held liable for any damages
** arising from the use of this software.
**
** Permission is granted to anyone to use this software for any purpose,
** including commercial applications, and to alter it and redistribute it
** freely, subject to the following restrictions:
**
** 1. The origin of this software must not be misrepresented; you must not
**    claim that you wrote the original software. If you use this software
**    in a product, an acknowledgment in the product documentation would be
**    appreciated but is not required.
** 2. Altered source versions must be plainly marked as such, and must not be
**    misrepresented as being the original software.
** 3. This notice may not be removed or altered from any source distribution.
**
**********************************************************/

#ifndef HELLOWIDGET_H
#define HELLOWIDGET_H

#include "qnanowidget.h"
#include "qnanopainter.h"
#include "painthelper.h"
#include <QVector>
#include <QPoint>
#include <QColor>

class QMouseEvent;

class HelloWidget : public QNanoWidget
{
    Q_OBJECT

public:
    HelloWidget()
    {
        setFillColor("#ffffff");
        // Enable mouse tracking for drawing
        setMouseTracking(true);
    }

    void paint(QNanoPainter *p)
    {
        // Paint the original hello item first
        // paintHelloItem(p, width(), height());

        // Paint user drawing paths
        paintDrawingPaths(p);
    }

protected:
    // Mouse event handlers for drawing
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:
    void paintDrawingPaths(QNanoPainter *p);

    // Drawing data
    QVector<QVector<QPoint>> m_drawingPaths;  // All completed paths
    QVector<QPoint> m_currentPath;            // Current path being drawn
    bool m_isDrawing = false;                 // Whether currently drawing
    QColor m_brushColor = QColor("#FF0000");  // Red brush color
    float m_brushWidth = 3.0f;                // Brush width

    // Optimization variables
    QPoint m_lastDrawPoint;                   // Last drawn point to avoid duplicate updates
    int m_updateThreshold = 3;                // Minimum pixel distance before update
    bool m_needsUpdate = false;               // Flag to control updates
};

#endif // HELLOWIDGET_H
