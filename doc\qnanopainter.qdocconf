
project     = "QNanoPainter"
description = "QNanoPainter Documentation"

outputdir = html

headerdirs  = ../libqnanopainter
headers.fileextensions = *.h

sourcedirs  = ../libqnanopainter
sources.fileextensions += *.cpp

# The exampledirs variable specifies the directories containing
# the source code of the example files.

#exampledirs = ../examples

# The imagedirs variable specifies the
# directories containing the images used in the documentation.

#imagedirs   = ../images
