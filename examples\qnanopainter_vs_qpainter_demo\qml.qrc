<RCC>
    <qresource prefix="/">
        <file>main.qml</file>
        <file>qml/Button.qml</file>
        <file>qml/FpsItem.qml</file>
        <file>qml/images/spinner.png</file>
        <file>qml/Switch.qml</file>
        <file>qml/SettingsView.qml</file>
        <file>qml/images/ic_settings_white_48dp.png</file>
        <file>qml/images/ic_cancel_white_48dp.png</file>
        <file>qml/TabView.qml</file>
        <file>qml/images/ic_pause_white_48dp.png</file>
        <file>qml/images/ic_play_arrow_white_48dp.png</file>
        <file>qml/images/circle.png</file>
        <file>qml/fonts/Roboto-Regular.ttf</file>
        <file>qml/SliderSelector.qml</file>
        <file>qml/shapeitem/DemoShapeItem.qml</file>
        <file>qml/shapeitem/BarsComponent.qml</file>
        <file>qml/shapeitem/RulerComponent.qml</file>
        <file>qml/shapeitem/CirclesComponent.qml</file>
        <file>qml/shapeitem/LinesComponent.qml</file>
        <file>qml/shapeitem/IconsComponent.qml</file>
        <file>qml/shapeitem/FlowerComponent.qml</file>
    </qresource>
</RCC>
