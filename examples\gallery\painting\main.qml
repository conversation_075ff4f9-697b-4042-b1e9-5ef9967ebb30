import QtQuick 2.3
import QtQuick.Controls 2.0
import QtQuick.Layouts 1.0
import QtQuick.Dialogs 1.2
import PaintingItem 1.0

Item {
    id: mainView

    property int margin: Math.min(width, height)*0.02

    // Top control panel
    Rectangle {
        id: controlPanel
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        height: 60
        color: "#2a2a2a"
        border.color: "#404040"
        border.width: 1

        RowLayout {
            anchors.fill: parent
            anchors.margins: 10
            spacing: 20

            // Brush width control
            Column {
                spacing: 5
                Text {
                    text: "线宽"
                    color: "#ffffff"
                    font.pixelSize: 12
                }
                Row {
                    spacing: 5
                    Slider {
                        id: widthSlider
                        from: 0.5
                        to: 10
                        value: 2
                        stepSize: 0.5
                        width: 100
                        onValueChanged: paintingItem.brushWidth = value
                    }
                    Text {
                        text: widthSlider.value.toFixed(1)
                        color: "#ffffff"
                        font.pixelSize: 10
                        anchors.verticalCenter: parent.verticalCenter
                    }
                }
            }

            // Brush color control
            Column {
                spacing: 5
                Text {
                    text: "画笔颜色"
                    color: "#ffffff"
                    font.pixelSize: 12
                }
                Rectangle {
                    width: 80
                    height: 25
                    color: paintingItem.brushColor
                    border.color: "#ffffff"
                    border.width: 1
                    MouseArea {
                        anchors.fill: parent
                        onClicked: brushColorDialog.open()
                    }
                    Text {
                        anchors.centerIn: parent
                        text: "选择颜色"
                        color: "#ffffff"
                        font.pixelSize: 10
                    }
                }
            }

            // Background color control
            Column {
                spacing: 5
                Text {
                    text: "背景颜色"
                    color: "#ffffff"
                    font.pixelSize: 12
                }
                Rectangle {
                    width: 80
                    height: 25
                    color: paintingItem.backgroundColor
                    border.color: "#ffffff"
                    border.width: 1
                    MouseArea {
                        anchors.fill: parent
                        onClicked: backgroundColorDialog.open()
                    }
                    Text {
                        anchors.centerIn: parent
                        text: "选择颜色"
                        color: paintingItem.backgroundColor == "#000000" ? "#ffffff" : "#000000"
                        font.pixelSize: 10
                    }
                }
            }
        }
    }

    PaintingItem {
        id: paintingItem
        anchors.top: controlPanel.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        mouseEventsEnabled: true
    }

    // Color dialogs
    ColorDialog {
        id: brushColorDialog
        title: "选择画笔颜色"
        color: paintingItem.brushColor
        onAccepted: {
            paintingItem.brushColor = color
        }
    }

    ColorDialog {
        id: backgroundColorDialog
        title: "选择背景颜色"
        color: paintingItem.backgroundColor
        onAccepted: {
            paintingItem.backgroundColor = color
        }
    }

}
