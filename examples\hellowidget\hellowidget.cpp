/**********************************************************
** Copyright (c) 2018 QUIt Coding <<EMAIL>>
**
** This software is provided 'as-is', without any express or implied
** warranty.  In no event will the authors be held liable for any damages
** arising from the use of this software.
**
** Permission is granted to anyone to use this software for any purpose,
** including commercial applications, and to alter it and redistribute it
** freely, subject to the following restrictions:
**
** 1. The origin of this software must not be misrepresented; you must not
**    claim that you wrote the original software. If you use this software
**    in a product, an acknowledgment in the product documentation would be
**    appreciated but is not required.
** 2. Altered source versions must be plainly marked as such, and must not be
**    misrepresented as being the original software.
** 3. This notice may not be removed or altered from any source distribution.
**
**********************************************************/

#include "hellowidget.h"
#include <QMouseEvent>

void HelloWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_isDrawing = true;
        m_currentPath.clear();
        m_currentPath.append(event->pos());
        update();
    }
    QNanoWidget::mousePressEvent(event);
}

void HelloWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isDrawing && (event->buttons() & Qt::LeftButton)) {
        m_currentPath.append(event->pos());
        update();
    }
    QNanoWidget::mouseMoveEvent(event);
}

void HelloWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_isDrawing) {
        m_isDrawing = false;
        if (m_currentPath.size() > 1) {
            // Save the completed path
            m_drawingPaths.append(m_currentPath);
        }
        m_currentPath.clear();
        update();
    }
    QNanoWidget::mouseReleaseEvent(event);
}

void HelloWidget::paintDrawingPaths(QNanoPainter *p)
{
    if (m_drawingPaths.isEmpty() && m_currentPath.isEmpty()) {
        return;
    }

    // Set up drawing style for user paths
    p->setLineCap(QNanoPainter::CAP_ROUND);
    p->setLineJoin(QNanoPainter::JOIN_ROUND);
    p->setLineWidth(m_brushWidth);
    p->setStrokeStyle(m_brushColor);

    // Draw all completed paths
    for (const auto& path : m_drawingPaths) {
        if (path.size() > 1) {
            p->beginPath();
            p->moveTo(path.first());
            for (int i = 1; i < path.size(); ++i) {
                p->lineTo(path.at(i));
            }
            p->stroke();
        }
    }

    // Draw current path being drawn
    if (m_currentPath.size() > 1) {
        p->beginPath();
        p->moveTo(m_currentPath.first());
        for (int i = 1; i < m_currentPath.size(); ++i) {
            p->lineTo(m_currentPath.at(i));
        }
        p->stroke();
    }
}
