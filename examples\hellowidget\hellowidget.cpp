/**********************************************************
** Copyright (c) 2018 QUIt Coding <<EMAIL>>
**
** This software is provided 'as-is', without any express or implied
** warranty.  In no event will the authors be held liable for any damages
** arising from the use of this software.
**
** Permission is granted to anyone to use this software for any purpose,
** including commercial applications, and to alter it and redistribute it
** freely, subject to the following restrictions:
**
** 1. The origin of this software must not be misrepresented; you must not
**    claim that you wrote the original software. If you use this software
**    in a product, an acknowledgment in the product documentation would be
**    appreciated but is not required.
** 2. Altered source versions must be plainly marked as such, and must not be
**    misrepresented as being the original software.
** 3. This notice may not be removed or altered from any source distribution.
**
**********************************************************/

#include "hellowidget.h"
#include "qnanocolor.h"
#include <QMouseEvent>

void HelloWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_isDrawing = true;
        m_currentPath.clear();
        m_currentPath.append(event->pos());
        m_lastDrawPoint = event->pos();

        // Initialize dirty rect for this drawing session
        m_currentDirtyRect = QRect(event->pos(), QSize(1, 1));
        m_currentDirtyRect = expandRectForBrush(m_currentDirtyRect);

        // Schedule update
        addDirtyRect(m_currentDirtyRect);
    }
    QNanoWidget::mousePressEvent(event);
}

void HelloWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isDrawing && (event->buttons() & Qt::LeftButton)) {
        QPoint currentPos = event->pos();

        // Only add point if it's far enough from last point (reduce redundant points)
        int distance = (currentPos - m_lastDrawPoint).manhattanLength();
        if (distance >= 2) {
            m_currentPath.append(currentPos);
            m_lastDrawPoint = currentPos;

            // Expand dirty rect to include new point
            QRect newPointRect(currentPos, QSize(1, 1));
            newPointRect = expandRectForBrush(newPointRect);
            m_currentDirtyRect = m_currentDirtyRect.united(newPointRect);

            // Schedule batched update
            addDirtyRect(m_currentDirtyRect);
        }
    }
    QNanoWidget::mouseMoveEvent(event);
}

void HelloWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_isDrawing) {
        m_isDrawing = false;
        if (m_currentPath.size() > 1) {
            // Save the completed path
            m_drawingPaths.append(m_currentPath);

            // Final dirty rect for the completed path
            QRect pathBounds = calculatePathBounds(m_currentPath);
            addDirtyRect(pathBounds);
        }
        m_currentPath.clear();
        m_currentDirtyRect = QRect();
    }
    QNanoWidget::mouseReleaseEvent(event);
}

void HelloWidget::paintDrawingPaths(QNanoPainter *p)
{
    if (m_drawingPaths.isEmpty() && m_currentPath.isEmpty()) {
        return;
    }

    // Set up drawing style for user paths
    p->setLineCap(QNanoPainter::CAP_ROUND);
    p->setLineJoin(QNanoPainter::JOIN_ROUND);
    p->setLineWidth(m_brushWidth);
    p->setStrokeStyle(QNanoColor::fromQColor(m_brushColor));

    // Draw all completed paths
    for (const auto& path : m_drawingPaths) {
        if (path.size() > 1) {
            p->beginPath();
            p->moveTo(path.first());
            for (int i = 1; i < path.size(); ++i) {
                p->lineTo(path.at(i));
            }
            p->stroke();
        }
    }

    // Draw current path being drawn
    if (m_currentPath.size() > 1) {
        p->beginPath();
        p->moveTo(m_currentPath.first());
        for (int i = 1; i < m_currentPath.size(); ++i) {
            p->lineTo(m_currentPath.at(i));
        }
        p->stroke();
    }
}

void HelloWidget::addDirtyRect(const QRect &rect)
{
    if (rect.isValid()) {
        m_pendingDirtyRects.append(rect);

        // Start timer for batched update if not already running
        if (!m_updateTimer.isActive()) {
            m_updateTimer.start();
        }
    }
}

void HelloWidget::performDirtyUpdate()
{
    if (m_pendingDirtyRects.isEmpty()) {
        return;
    }

    // Merge all pending dirty rects
    QRect combinedRect = m_pendingDirtyRects.first();
    for (int i = 1; i < m_pendingDirtyRects.size(); ++i) {
        combinedRect = combinedRect.united(m_pendingDirtyRects[i]);
    }

    // Clear pending rects
    m_pendingDirtyRects.clear();

    // Ensure the rect is within widget bounds
    combinedRect = combinedRect.intersected(rect());

    // Perform the actual update on the dirty region
    if (!combinedRect.isEmpty()) {
        update(combinedRect);
    }
}

QRect HelloWidget::calculatePathBounds(const QVector<QPoint> &path) const
{
    if (path.isEmpty()) {
        return QRect();
    }

    QPoint topLeft = path.first();
    QPoint bottomRight = path.first();

    for (const QPoint &point : path) {
        topLeft.setX(qMin(topLeft.x(), point.x()));
        topLeft.setY(qMin(topLeft.y(), point.y()));
        bottomRight.setX(qMax(bottomRight.x(), point.x()));
        bottomRight.setY(qMax(bottomRight.y(), point.y()));
    }

    QRect bounds(topLeft, bottomRight);
    return expandRectForBrush(bounds);
}

QRect HelloWidget::expandRectForBrush(const QRect &rect) const
{
    // Expand rect to account for brush width and anti-aliasing
    int margin = static_cast<int>(m_brushWidth / 2) + 2;
    return rect.adjusted(-margin, -margin, margin, margin);
}
